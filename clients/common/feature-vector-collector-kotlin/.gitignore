# Gradle build artifacts
build/
.gradle/

# Gradle wrapper (keep gradlew and gradle/wrapper/ but ignore cache)
.gradle/
gradle-app.setting
!gradle-wrapper.jar

# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr
out/

# Eclipse
.project
.classpath
.settings/
bin/

# VS Code
.vscode/

# NetBeans
nbproject/
nbbuild/
dist/
nbdist/
.nb-gradle/

# Compiled class files
*.class

# JAR files (except gradle wrapper)
*.jar
!gradle/wrapper/gradle-wrapper.jar

# WAR files
*.war

# EAR files
*.ear

# Log files
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Kotlin/Native
*.kexe
*.klib

# ProGuard obfuscation artifacts (keep mapping files for debugging)
# Note: You may want to keep mapping.txt for production debugging
# Uncomment the line below if you want to ignore mapping files too
# build/obfuscated/mapping.txt

# Gradle daemon
.gradle/daemon/

# Gradle build cache
.gradle/buildOutputCleanup/
.gradle/checksums/
.gradle/configuration-cache/
.gradle/file-system.probe

# Local configuration file (sdk path, etc)
local.properties

# Android Studio generated files and folders
captures/
.externalNativeBuild/
.cxx/
*.apk
output.json

# Keystore files
*.jks
*.keystore

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json
